#!/usr/bin/env python3
"""Simple test to check imports"""

import sys
import os

print("Python version:", sys.version)
print("Current directory:", os.getcwd())

try:
    import utils
    print("✓ utils imported successfully")
    
    categories = utils.list_categories()
    print(f"✓ Found {len(categories)} categories: {categories}")
except Exception as e:
    print(f"✗ Error importing utils: {e}")

try:
    from jinja2 import Environment, FileSystemLoader
    print("✓ Jinja2 imported successfully")
except Exception as e:
    print(f"✗ Error importing Jinja2: {e}")

try:
    import html_generator_service
    print("✓ html_generator_service imported successfully")
except Exception as e:
    print(f"✗ Error importing html_generator_service: {e}")
    import traceback
    traceback.print_exc()

print("Test completed")
