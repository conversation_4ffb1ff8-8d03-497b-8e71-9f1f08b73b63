#!/usr/bin/env python3
"""
Test script for the HTML Generator Service

This script tests the HTML generator functionality without running the full Flask app.
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_html_generator():
    """Test the HTML generator service."""
    print("Testing HTML Generator Service...")
    
    try:
        # Import the service
        from html_generator_service import HTMLGeneratorService
        print("✓ HTML Generator Service imported successfully")
        
        # Create a temporary frontend directory for testing
        test_frontend_dir = tempfile.mkdtemp(prefix="test_frontend_")
        print(f"✓ Created test frontend directory: {test_frontend_dir}")
        
        # Initialize the service
        generator = HTMLGeneratorService()
        
        # Override the frontend directory for testing
        original_frontend_dir = generator.__class__.__module__
        generator.FRONTEND_DIR = test_frontend_dir
        
        print("✓ HTML Generator Service initialized")
        
        # Test configuration validation
        print("\n--- Testing Configuration Validation ---")
        
        # Test invalid configuration
        invalid_config = {
            'ai_model': '',
            'anti_hallucination_mode': 'invalid',
            'categories': [],
            'filename': ''
        }
        
        is_valid, error_msg = generator.validate_configuration(invalid_config)
        if not is_valid:
            print(f"✓ Invalid configuration correctly rejected: {error_msg}")
        else:
            print("✗ Invalid configuration was incorrectly accepted")
            return False
        
        # Test valid configuration
        valid_config = {
            'ai_model': 'llama3.1:8b-instruct-q4_K_M',
            'anti_hallucination_mode': 'strict',
            'categories': ['MANUAL'],  # Assuming MANUAL category exists
            'filename': 'test-interface'
        }
        
        is_valid, error_msg = generator.validate_configuration(valid_config)
        if is_valid:
            print("✓ Valid configuration correctly accepted")
        else:
            print(f"✗ Valid configuration was incorrectly rejected: {error_msg}")
            return False
        
        # Test file listing (should be empty initially)
        print("\n--- Testing File Listing ---")
        files = generator.list_generated_files()
        if isinstance(files, list):
            print(f"✓ File listing works (found {len(files)} files)")
        else:
            print("✗ File listing failed")
            return False
        
        print("\n--- Testing HTML Generation ---")
        
        # Create a minimal test template
        test_template_content = """<!DOCTYPE html>
<html>
<head>
    <title>Test Template</title>
</head>
<body>
    <div class="input-group">
        <label for="category">Category</label>
        <select id="category">
            {% for category in categories %}
            <option value="{{ category }}">{{ category }}</option>
            {% endfor %}
        </select>
    </div>
    
    <div class="mode-selector">
        <input type="radio" name="anti_hallucination_mode" value="strict" checked>
        <input type="radio" name="anti_hallucination_mode" value="balanced">
        <input type="radio" name="anti_hallucination_mode" value="creative">
    </div>
    
    <div class="model-selector">
        <select id="model-selector">
            <option value="llama3.1:8b">Llama 3.1 8B</option>
        </select>
    </div>
</body>
</html>"""
        
        # Create a test templates directory
        test_templates_dir = os.path.join(test_frontend_dir, 'templates')
        os.makedirs(test_templates_dir, exist_ok=True)
        
        test_template_path = os.path.join(test_templates_dir, 'index.html')
        with open(test_template_path, 'w', encoding='utf-8') as f:
            f.write(test_template_content)
        
        # Override the templates directory
        generator.jinja_env.loader.searchpath = [test_templates_dir]
        
        print("✓ Test template created")
        
        # Test HTML generation
        test_config = {
            'ai_model': 'llama3.1:8b-instruct-q4_K_M',
            'anti_hallucination_mode': 'balanced',
            'categories': ['TEST_CATEGORY'],
            'filename': 'test-generated-interface.html'
        }
        
        # Mock the utils.list_categories function
        import utils
        original_list_categories = utils.list_categories
        utils.list_categories = lambda: ['TEST_CATEGORY', 'MANUAL', 'RISE']
        
        try:
            success, message = generator.generate_html(test_config)
            if success:
                print(f"✓ HTML generation successful: {message}")
                
                # Check if file was created
                generated_file = os.path.join(test_frontend_dir, test_config['filename'])
                if os.path.exists(generated_file):
                    print("✓ Generated file exists")
                    
                    # Read and check the generated content
                    with open(generated_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for expected modifications
                    if 'GENERATED_CONFIG' in content:
                        print("✓ Configuration injection found")
                    else:
                        print("✗ Configuration injection missing")
                    
                    if 'Generated HTML Interface' in content:
                        print("✓ Generation metadata found")
                    else:
                        print("✗ Generation metadata missing")
                    
                    print(f"✓ Generated file size: {len(content)} characters")
                    
                else:
                    print("✗ Generated file not found")
                    return False
            else:
                print(f"✗ HTML generation failed: {message}")
                return False
        finally:
            # Restore original function
            utils.list_categories = original_list_categories
        
        # Test file listing after generation
        files = generator.list_generated_files()
        if len(files) > 0:
            print(f"✓ File listing after generation: {len(files)} files")
        else:
            print("✗ No files found after generation")
        
        # Test file deletion
        print("\n--- Testing File Deletion ---")
        success, message = generator.delete_generated_file(test_config['filename'])
        if success:
            print(f"✓ File deletion successful: {message}")
        else:
            print(f"✗ File deletion failed: {message}")
        
        print("\n✓ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test directory
        if 'test_frontend_dir' in locals():
            try:
                shutil.rmtree(test_frontend_dir)
                print(f"✓ Cleaned up test directory: {test_frontend_dir}")
            except Exception as e:
                print(f"⚠ Failed to clean up test directory: {e}")

if __name__ == "__main__":
    success = test_html_generator()
    sys.exit(0 if success else 1)
