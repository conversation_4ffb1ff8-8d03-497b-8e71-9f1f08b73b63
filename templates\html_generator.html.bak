{% extends "admin_base.html" %}

{% block title %}HTML Generator{% endblock %}
{% block content_title %}HTML Generator{% endblock %}

{% block head %}

<style>
    .config-section {
        background: var(--bs-dark);
        border: 1px solid var(--bs-border-color);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .config-section h5 {
        color: var(--bs-primary);
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .form-label {
        color: var(--bs-light);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        background-color: var(--bs-dark);
        border: 1px solid var(--bs-border-color);
        color: var(--bs-light);
    }
    
    .form-control:focus, .form-select:focus {
        background-color: var(--bs-dark);
        border-color: var(--bs-primary);
        color: var(--bs-light);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .category-checkbox {
        margin-bottom: 0.5rem;
    }
    
    .category-checkbox .form-check-input {
        background-color: var(--bs-dark);
        border: 1px solid var(--bs-border-color);
    }
    
    .category-checkbox .form-check-input:checked {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }
    
    .category-checkbox .form-check-label {
        color: var(--bs-light);
        margin-left: 0.5rem;
    }
    
    .preview-section {
        background: var(--bs-gray-900);
        border: 1px solid var(--bs-border-color);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .preview-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--bs-border-color);
    }
    
    .preview-item:last-child {
        border-bottom: none;
    }
    
    .preview-label {
        color: var(--bs-secondary);
        font-weight: 500;
    }
    
    .preview-value {
        color: var(--bs-light);
    }
    
    .badge-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .generated-files-table {
        background: var(--bs-dark);
        border: 1px solid var(--bs-border-color);
        border-radius: 0.5rem;
    }
    
    .generated-files-table th {
        background: var(--bs-gray-800);
        color: var(--bs-light);
        border-color: var(--bs-border-color);
    }
    
    .generated-files-table td {
        color: var(--bs-light);
        border-color: var(--bs-border-color);
    }
    
    .btn-generate {
        background: linear-gradient(45deg, var(--bs-primary), var(--bs-info));
        border: none;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
        color: white;
    }
    
    .btn-generate:disabled {
        background: var(--bs-secondary);
        transform: none;
        box-shadow: none;
    }
    
    .loading-spinner {
        display: none;
        margin-left: 0.5rem;
    }
    
    .error-message {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid var(--bs-danger);
        color: var(--bs-danger);
        padding: 0.75rem;
        border-radius: 0.5rem;
        margin-top: 1rem;
    }
    
    .success-message {
        background: rgba(25, 135, 84, 0.1);
        border: 1px solid var(--bs-success);
        color: var(--bs-success);
        padding: 0.75rem;
        border-radius: 0.5rem;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-light mb-1">HTML Generator</h2>
                    <p class="text-muted mb-0">Generate customized HTML chat interfaces with pre-configured settings</p>
                </div>
            </div>
            
            <!-- Configuration Form -->
            <form id="htmlGeneratorForm">
			<input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- AI Model Selection -->
                        <div class="config-section">
                            <h5><i class="fas fa-robot me-2"></i>AI Model Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="aiModel" class="form-label">Select AI Model</label>
                                    <select id="aiModel" name="ai_model" class="form-select" required>
                                        <option value="">Loading models...</option>
                                    </select>
                                    <div class="form-text text-muted">Choose the AI model for the generated interface</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Model Information</label>
                                    <div id="modelInfo" class="text-muted">
                                        <small>Select a model to view details</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Anti-Hallucination Mode -->
                        <div class="config-section">
                            <h5><i class="fas fa-shield-alt me-2"></i>Response Mode Configuration</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="anti_hallucination_mode" id="modeStrict" value="strict" checked>
                                        <label class="form-check-label" for="modeStrict">
                                            <strong>Strict</strong>
                                            <div class="text-muted small">Only information from documents</div>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="anti_hallucination_mode" id="modeBalanced" value="balanced">
                                        <label class="form-check-label" for="modeBalanced">
                                            <strong>Balanced</strong>
                                            <div class="text-muted small">Limited reasonable inferences</div>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="anti_hallucination_mode" id="modeCreative" value="creative">
                                        <label class="form-check-label" for="modeCreative">
                                            <strong>Creative</strong>
                                            <div class="text-muted small">More flexible responses</div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Document Categories -->
                        <div class="config-section">
                            <h5><i class="fas fa-folder me-2"></i>Document Categories</h5>
                            <div class="row" id="categoriesContainer">
                                {% for category in categories %}
                                <div class="col-md-4 category-checkbox">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="categories" value="{{ category }}" id="category_{{ loop.index }}">
                                        <label class="form-check-label" for="category_{{ loop.index }}">
                                            {{ category }}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="form-text text-muted">Select one or more categories for simultaneous context retrieval</div>
                        </div>
                        
                        <!-- Output Configuration -->
                        <div class="config-section">
                            <h5><i class="fas fa-file-code me-2"></i>Output Configuration</h5>
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="filename" class="form-label">Output Filename</label>
                                    <input type="text" id="filename" name="filename" class="form-control" placeholder="my-custom-interface" required>
                                    <div class="form-text text-muted">Filename for the generated HTML (extension will be added automatically)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- Configuration Preview -->
                        <div class="config-section">
                            <h5><i class="fas fa-eye me-2"></i>Configuration Preview</h5>
                            <div class="preview-section">
                                <div class="preview-item">
                                    <span class="preview-label">AI Model:</span>
                                    <span class="preview-value" id="previewModel">Not selected</span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Response Mode:</span>
                                    <span class="preview-value" id="previewMode">Strict</span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Categories:</span>
                                    <div class="preview-value">
                                        <div id="previewCategories" class="badge-container">
                                            <span class="text-muted">None selected</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Output File:</span>
                                    <span class="preview-value" id="previewFilename">Not specified</span>
                                </div>
                            </div>
                            
                            <!-- Generate Button -->
                            <div class="d-grid mt-3">
                                <button type="submit" class="btn btn-generate" id="generateBtn">
                                    <i class="fas fa-magic me-2"></i>Generate HTML Interface
                                    <div class="loading-spinner spinner-border spinner-border-sm" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            
            <!-- Messages -->
            <div id="messageContainer"></div>
            
            <!-- Generated Files List -->
            <div class="config-section mt-4">
                <h5><i class="fas fa-list me-2"></i>Generated Files</h5>
                <div class="table-responsive">
                    <table class="table table-dark generated-files-table">
                        <thead>
                            <tr>
                                <th>Filename</th>
                                <th>Size</th>
                                <th>Created</th>
                                <th>Modified</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="generatedFilesTable">
                            <tr>
                                <td colspan="5" class="text-center text-muted">Loading...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the HTML Generator interface
    initializeHTMLGenerator();
});

function initializeHTMLGenerator() {
    loadAvailableModels();
    loadGeneratedFiles();
    setupEventListeners();
    updatePreview();
}

function setupEventListeners() {
    // Form submission
    document.getElementById('htmlGeneratorForm').addEventListener('submit', handleFormSubmit);
    
    // Preview updates
    document.getElementById('aiModel').addEventListener('change', updatePreview);
    document.querySelectorAll('input[name="anti_hallucination_mode"]').forEach(radio => {
        radio.addEventListener('change', updatePreview);
    });
    document.querySelectorAll('input[name="categories"]').forEach(checkbox => {
        checkbox.addEventListener('change', updatePreview);
    });
    document.getElementById('filename').addEventListener('input', updatePreview);
}

async function loadAvailableModels() {
    try {
        const response = await fetch('/api/available-models');
        const data = await response.json();
        
        const modelSelect = document.getElementById('aiModel');
        modelSelect.innerHTML = '<option value="">Select a model...</option>';
        
        if (data.success && data.models) {
            data.models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = `${model.display_name} (${model.size_formatted})`;
                if (model.name === data.default_model) {
                    option.selected = true;
                }
                modelSelect.appendChild(option);
            });
        }
        
        updatePreview();
    } catch (error) {
        console.error('Error loading models:', error);
        showMessage('Error loading available models', 'error');
    }
}

async function loadGeneratedFiles() {
    try {
        const response = await fetch('/admin/html-generator/files');
        const data = await response.json();
        
        const tbody = document.getElementById('generatedFilesTable');
        tbody.innerHTML = '';
        
        if (data.success && data.files && data.files.length > 0) {
            data.files.forEach(file => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${file.filename}</td>
                    <td>${formatFileSize(file.size)}</td>
                    <td>${file.created}</td>
                    <td>${file.modified}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="viewFile('${file.filename}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('${file.filename}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No generated files found</td></tr>';
        }
    } catch (error) {
        console.error('Error loading generated files:', error);
        document.getElementById('generatedFilesTable').innerHTML = 
            '<tr><td colspan="5" class="text-center text-danger">Error loading files</td></tr>';
    }
}

function updatePreview() {
    // Update model preview
    const modelSelect = document.getElementById('aiModel');
    const selectedModel = modelSelect.options[modelSelect.selectedIndex];
    document.getElementById('previewModel').textContent = 
        selectedModel && selectedModel.value ? selectedModel.textContent : 'Not selected';
    
    // Update mode preview
    const selectedMode = document.querySelector('input[name="anti_hallucination_mode"]:checked');
    document.getElementById('previewMode').textContent = 
        selectedMode ? selectedMode.value.charAt(0).toUpperCase() + selectedMode.value.slice(1) : 'Not selected';
    
    // Update categories preview
    const selectedCategories = Array.from(document.querySelectorAll('input[name="categories"]:checked'))
        .map(cb => cb.value);
    const categoriesContainer = document.getElementById('previewCategories');
    
    if (selectedCategories.length > 0) {
        categoriesContainer.innerHTML = selectedCategories
            .map(cat => `<span class="badge bg-primary me-1">${cat}</span>`)
            .join('');
    } else {
        categoriesContainer.innerHTML = '<span class="text-muted">None selected</span>';
    }
    
    // Update filename preview
    const filename = document.getElementById('filename').value.trim();
    document.getElementById('previewFilename').textContent = 
        filename ? (filename.endsWith('.html') ? filename : filename + '.html') : 'Not specified';
}

async function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const config = {
        ai_model: formData.get('ai_model'),
        anti_hallucination_mode: formData.get('anti_hallucination_mode'),
        categories: formData.getAll('categories'),
        filename: formData.get('filename')
    };
    
    // Validate form
    if (!config.ai_model) {
        showMessage('Please select an AI model', 'error');
        return;
    }
    
    if (config.categories.length === 0) {
        showMessage('Please select at least one category', 'error');
        return;
    }
    
    if (!config.filename) {
        showMessage('Please enter a filename', 'error');
        return;
    }
    
    // Show loading state
    const generateBtn = document.getElementById('generateBtn');
    const spinner = generateBtn.querySelector('.loading-spinner');
    generateBtn.disabled = true;
    spinner.style.display = 'inline-block';
    
    try {
        const response = await fetch('/admin/html-generator/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(config)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            // Reset form
            event.target.reset();
            updatePreview();
            // Reload files list
            loadGeneratedFiles();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Error generating HTML:', error);
        showMessage('Error generating HTML interface', 'error');
    } finally {
        // Hide loading state
        generateBtn.disabled = false;
        spinner.style.display = 'none';
    }
}

function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = `${type}-message`;
    messageDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>
    `;
    
    container.innerHTML = '';
    container.appendChild(messageDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 5000);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function viewFile(filename) {
    window.open(`/frontend/${filename}`, '_blank');
}

async function deleteFile(filename) {
    if (!confirm(`Are you sure you want to delete "${filename}"?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/html-generator/delete/${filename}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            loadGeneratedFiles();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Error deleting file:', error);
        showMessage('Error deleting file', 'error');
    }
}
</script>
{% endblock %}
