#!/usr/bin/env python3
"""
Validation script for HTML Generator implementation

This script validates that all components are properly implemented.
"""

import os
import re
from pathlib import Path

def validate_files():
    """Validate that all required files exist and have correct content."""
    print("=== Validating HTML Generator Implementation ===\n")
    
    # Check if required files exist
    required_files = [
        'html_generator_service.py',
        'templates/html_generator.html',
        'app.py',
        'permissions.py',
        'templates/admin_base.html',
        'templates/admin_dashboard.html'
    ]
    
    print("1. Checking required files...")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✓ {file_path}")
        else:
            print(f"   ✗ {file_path} - MISSING")
            return False
    
    # Check permissions.py for html_generator permission
    print("\n2. Checking permissions configuration...")
    with open('permissions.py', 'r', encoding='utf-8') as f:
        permissions_content = f.read()
    
    if "'html_generator'" in permissions_content:
        print("   ✓ html_generator permission found in permissions.py")
    else:
        print("   ✗ html_generator permission missing from permissions.py")
        return False
    
    # Check admin_base.html for navigation link
    print("\n3. Checking admin navigation...")
    with open('templates/admin_base.html', 'r', encoding='utf-8') as f:
        admin_base_content = f.read()
    
    if 'html_generator' in admin_base_content and 'HTML Generator' in admin_base_content:
        print("   ✓ HTML Generator navigation link found in admin_base.html")
    else:
        print("   ✗ HTML Generator navigation link missing from admin_base.html")
        return False
    
    # Check app.py for routes
    print("\n4. Checking Flask routes...")
    with open('app.py', 'r', encoding='utf-8') as f:
        app_content = f.read()
    
    required_routes = [
        '/admin/html-generator',
        '/admin/html-generator/generate',
        '/admin/html-generator/files',
        '/admin/html-generator/delete'
    ]
    
    for route in required_routes:
        if route in app_content:
            print(f"   ✓ Route {route} found")
        else:
            print(f"   ✗ Route {route} missing")
            return False
    
    # Check for html_generator import in app.py
    if 'from html_generator_service import html_generator' in app_content:
        print("   ✓ html_generator service import found in app.py")
    else:
        print("   ✗ html_generator service import missing from app.py")
        return False
    
    # Check HTML template structure
    print("\n5. Checking HTML template...")
    with open('templates/html_generator.html', 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    required_elements = [
        'extends "admin_base.html"',
        'id="htmlGeneratorForm"',
        'id="aiModel"',
        'name="anti_hallucination_mode"',
        'name="categories"',
        'id="filename"',
        'loadAvailableModels',
        'handleFormSubmit'
    ]
    
    for element in required_elements:
        if element in template_content:
            print(f"   ✓ Template element '{element}' found")
        else:
            print(f"   ✗ Template element '{element}' missing")
            return False
    
    # Check service class structure
    print("\n6. Checking service class...")
    with open('html_generator_service.py', 'r', encoding='utf-8') as f:
        service_content = f.read()
    
    required_methods = [
        'class HTMLGeneratorService',
        'def validate_configuration',
        'def generate_html',
        'def list_generated_files',
        'def delete_generated_file',
        'def _process_generated_html'
    ]
    
    for method in required_methods:
        if method in service_content:
            print(f"   ✓ Service method '{method}' found")
        else:
            print(f"   ✗ Service method '{method}' missing")
            return False
    
    # Check frontend directory
    print("\n7. Checking frontend directory...")
    if os.path.exists('frontend'):
        print("   ✓ frontend directory exists")
    else:
        print("   ✗ frontend directory missing")
        return False
    
    # Check dashboard card
    print("\n8. Checking dashboard card...")
    with open('templates/admin_dashboard.html', 'r', encoding='utf-8') as f:
        dashboard_content = f.read()
    
    if 'html_generator_page' in dashboard_content and 'HTML Generator' in dashboard_content:
        print("   ✓ HTML Generator dashboard card found")
    else:
        print("   ✗ HTML Generator dashboard card missing")
        return False
    
    print("\n✓ All validation checks passed!")
    print("\n=== Implementation Summary ===")
    print("• HTML Generator Service: Implemented")
    print("• Flask Routes: Implemented")
    print("• Admin Interface: Implemented")
    print("• Permissions: Configured")
    print("• Navigation: Added")
    print("• Dashboard Card: Added")
    print("• Frontend Directory: Ready")
    
    return True

def check_dependencies():
    """Check if required dependencies are available."""
    print("\n=== Checking Dependencies ===")
    
    dependencies = [
        'jinja2',
        'flask',
        'werkzeug'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✓ {dep}")
        except ImportError:
            print(f"   ✗ {dep} - Not available")
            return False
    
    return True

def main():
    """Main validation function."""
    print("HTML Generator Implementation Validator")
    print("=" * 50)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    success = True
    
    # Validate files and implementation
    if not validate_files():
        success = False
    
    # Check dependencies
    if not check_dependencies():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✓ HTML Generator implementation is complete and ready!")
        print("\nNext steps:")
        print("1. Start the Flask application")
        print("2. Log in as an admin user")
        print("3. Navigate to Admin Dashboard > Content Management > HTML Generator")
        print("4. Configure and generate your first HTML interface")
    else:
        print("✗ Implementation validation failed!")
        print("Please fix the issues above before proceeding.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
